Page({
  /**
   * 页面的初始数据
   */
  data: {
    phoneNumber: '************', // 自习室联系电话
    address: '浙江省杭州市西湖区西湖街道西湖小区西湖xx',
    latitude: 30.2741, // 杭州西湖区纬度
    longitude: 120.1551 // 杭州西湖区经度
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    console.info("首页加载");
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {
    console.info("首页渲染完成");
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    console.info("首页显示");
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {
    console.info("首页隐藏");
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {
    console.info("首页卸载");
  },

  /**
   * 一键拨打电话事件处理函数
   */
  onCallPhone: function(e) {
    const phoneNumber = this.data.phoneNumber;

    wx.showModal({
      title: '拨打电话',
      content: `确定要拨打 ${phoneNumber} 吗？`,
      success: (res) => {
        if (res.confirm) {
          wx.makePhoneCall({
            phoneNumber: phoneNumber,
            success: () => {
              console.log('拨打电话成功');
            },
            fail: (err) => {
              console.error('拨打电话失败:', err);
              wx.showToast({
                title: '拨打失败',
                icon: 'error',
                duration: 2000
              });
            }
          });
        }
      }
    });
  },

  /**
   * 一键查看地图事件处理函数
   */
  onViewMap: function(e) {
    const { latitude, longitude, address } = this.data;

    wx.openLocation({
      latitude: latitude,
      longitude: longitude,
      name: '全部都能考得上自习室',
      address: address,
      scale: 18,
      success: () => {
        console.log('打开地图成功');
      },
      fail: (err) => {
        console.error('打开地图失败:', err);
        wx.showToast({
          title: '打开地图失败',
          icon: 'error',
          duration: 2000
        });
      }
    });
  }
});
